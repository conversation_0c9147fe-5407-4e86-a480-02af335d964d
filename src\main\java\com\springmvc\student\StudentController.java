package com.springmvc.student;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 学生信息控制器类
 * 处理学生信息的添加、显示和照片的上传下载
 */
@Controller
@RequestMapping("/student")
public class StudentController {
    
    // 照片存储路径
    private static final String UPLOAD_PATH = "uploads/photos/";
    
    /**
     * 显示添加学生信息页面
     */
    @GetMapping("/add")
    public String showAddStudentPage() {
        return "studentpages/addStudent";
    }
    
    /**
     * 处理学生信息提交和照片上传
     */
    @PostMapping("/save")
    public String saveStudent(@RequestParam("studentId") String studentId,
                             @RequestParam("name") String name,
                             @RequestParam("grade") String grade,
                             @RequestParam("className") String className,
                             @RequestParam("gender") String gender,
                             @RequestParam("age") int age,
                             @RequestParam("photo") MultipartFile photo,
                             Model model,
                             HttpServletRequest request) {
        
        // 创建学生对象
        Student student = new Student(studentId, name, grade, className, gender, age);
        
        // 处理照片上传
        if (!photo.isEmpty()) {
            try {
                // 获取上传文件的原始名称
                String originalFilename = photo.getOriginalFilename();
                String fileExtension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
                }
                
                // 生成新的文件名（使用学号）
                String newFileName = studentId + fileExtension;
                
                // 创建上传目录
                String realPath = request.getServletContext().getRealPath("/");
                String uploadDir = realPath + UPLOAD_PATH;
                File uploadDirFile = new File(uploadDir);
                if (!uploadDirFile.exists()) {
                    uploadDirFile.mkdirs();
                }
                
                // 保存文件
                String filePath = uploadDir + newFileName;
                File destFile = new File(filePath);
                photo.transferTo(destFile);
                
                // 设置学生照片信息
                student.setPhotoName(newFileName);
                student.setPhotoPath(UPLOAD_PATH + newFileName);
                
                // 读取照片数据（用于显示）
                byte[] photoData = Files.readAllBytes(Paths.get(filePath));
                student.setPhoto(photoData);
                
            } catch (IOException e) {
                e.printStackTrace();
                model.addAttribute("error", "照片上传失败：" + e.getMessage());
                return "studentpages/addStudent";
            }
        }
        
        // 将学生信息添加到模型中
        model.addAttribute("student", student);
        model.addAttribute("message", "学生信息保存成功！");
        
        return "studentpages/showStudent";
    }
    
    /**
     * 下载学生照片
     */
    @GetMapping("/download/{fileName}")
    public void downloadPhoto(@PathVariable("fileName") String fileName,
                             HttpServletRequest request,
                             HttpServletResponse response) {
        
        try {
            // 获取文件路径
            String realPath = request.getServletContext().getRealPath("/");
            String filePath = realPath + UPLOAD_PATH + fileName;
            File file = new File(filePath);
            
            if (!file.exists()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件不存在");
                return;
            }
            
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setContentLength((int) file.length());
            
            // 读取文件并写入响应流
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
            
        } catch (IOException e) {
            e.printStackTrace();
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "下载失败");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }
    
    /**
     * 显示照片（用于在页面中显示）
     */
    @GetMapping("/photo/{fileName}")
    public void showPhoto(@PathVariable("fileName") String fileName,
                         HttpServletRequest request,
                         HttpServletResponse response) {
        
        try {
            // 获取文件路径
            String realPath = request.getServletContext().getRealPath("/");
            String filePath = realPath + UPLOAD_PATH + fileName;
            File file = new File(filePath);
            
            if (!file.exists()) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "图片不存在");
                return;
            }
            
            // 设置响应类型为图片
            String contentType = Files.probeContentType(Paths.get(filePath));
            if (contentType == null) {
                contentType = "image/jpeg"; // 默认类型
            }
            response.setContentType(contentType);
            
            // 读取文件并写入响应流
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }
            
        } catch (IOException e) {
            e.printStackTrace();
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "图片加载失败");
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }
}

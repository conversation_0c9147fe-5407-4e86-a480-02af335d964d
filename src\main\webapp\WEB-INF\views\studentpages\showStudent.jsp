<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生信息详情</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .student-info {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        
        .info-item {
            display: flex;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .info-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
            width: 100px;
            flex-shrink: 0;
        }
        
        .info-value {
            color: #333;
            flex: 1;
        }
        
        .photo-section {
            text-align: center;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
        
        .photo-container {
            margin-bottom: 20px;
        }
        
        .student-photo {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border: 2px solid #ddd;
        }
        
        .no-photo {
            width: 200px;
            height: 250px;
            background-color: #e9ecef;
            border: 2px dashed #adb5bd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
            margin: 0 auto;
        }
        
        .download-btn {
            background-color: #2196F3;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin-top: 10px;
            transition: background-color 0.3s;
        }
        
        .download-btn:hover {
            background-color: #1976D2;
            text-decoration: none;
            color: white;
        }
        
        .download-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        
        .btn {
            padding: 12px 25px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #45a049;
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
            text-decoration: none;
            color: white;
        }
        
        @media (max-width: 768px) {
            .student-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>学生信息详情</h1>
        
        <!-- 成功消息 -->
        <c:if test="${not empty message}">
            <div class="success-message">
                <strong>✓</strong> ${message}
            </div>
        </c:if>
        
        <div class="student-info">
            <!-- 学生基本信息 -->
            <div class="info-section">
                <h3 style="margin-top: 0; color: #4CAF50;">基本信息</h3>
                
                <div class="info-item">
                    <span class="info-label">学号：</span>
                    <span class="info-value">${student.studentId}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">姓名：</span>
                    <span class="info-value">${student.name}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">年级：</span>
                    <span class="info-value">${student.grade}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">班级：</span>
                    <span class="info-value">${student.className}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">性别：</span>
                    <span class="info-value">${student.gender}</span>
                </div>
                
                <div class="info-item">
                    <span class="info-label">年龄：</span>
                    <span class="info-value">${student.age} 岁</span>
                </div>
            </div>
            
            <!-- 照片信息 -->
            <div class="photo-section">
                <h3 style="margin-top: 0; color: #2196F3;">学生照片</h3>
                
                <div class="photo-container">
                    <c:choose>
                        <c:when test="${not empty student.photoName}">
                            <img src="${pageContext.request.contextPath}/student/photo/${student.photoName}" 
                                 alt="学生照片" class="student-photo" 
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="no-photo" style="display: none;">
                                照片加载失败
                            </div>
                        </c:when>
                        <c:otherwise>
                            <div class="no-photo">
                                暂无照片
                            </div>
                        </c:otherwise>
                    </c:choose>
                </div>
                
                <c:if test="${not empty student.photoName}">
                    <div>
                        <p><strong>文件名：</strong>${student.photoName}</p>
                        <a href="${pageContext.request.contextPath}/student/download/${student.photoName}" 
                           class="download-btn">
                            📥 下载照片
                        </a>
                    </div>
                </c:if>
                
                <c:if test="${empty student.photoName}">
                    <p style="color: #6c757d; margin-top: 10px;">未上传照片</p>
                </c:if>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons">
            <a href="${pageContext.request.contextPath}/student/add" class="btn btn-primary">
                ➕ 添加新学生
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                ⬅️ 返回上页
            </a>
        </div>
    </div>
    
    <script>
        // 图片加载错误处理
        document.addEventListener('DOMContentLoaded', function() {
            const img = document.querySelector('.student-photo');
            if (img) {
                img.addEventListener('error', function() {
                    this.style.display = 'none';
                    const noPhoto = this.nextElementSibling;
                    if (noPhoto) {
                        noPhoto.style.display = 'flex';
                        noPhoto.textContent = '照片加载失败';
                    }
                });
            }
        });
    </script>
</body>
</html>

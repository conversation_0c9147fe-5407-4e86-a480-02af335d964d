# 学生管理系统 - Spring MVC 文件上传下载实验

## 项目简介

这是一个基于Spring MVC框架开发的学生管理系统，主要实现了学生信息的录入、照片上传和下载功能。该项目是Web应用开发课程的实验项目。

## 功能特性

- ✅ 学生信息录入（学号、姓名、年级、班级、性别、年龄）
- ✅ 学生照片上传功能
- ✅ 照片在线预览
- ✅ 照片文件下载
- ✅ 响应式界面设计
- ✅ 文件类型和大小验证
- ✅ 错误处理和用户友好提示

## 技术栈

- **后端框架**: Spring MVC 5.3.21
- **视图技术**: JSP + JSTL
- **文件上传**: Apache Commons FileUpload
- **构建工具**: Maven
- **服务器**: Apache Tomcat
- **前端**: HTML5 + CSS3 + JavaScript

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/springmvc/student/
│   │       ├── Student.java              # 学生实体类
│   │       └── StudentController.java    # 控制器类
│   └── webapp/
│       ├── WEB-INF/
│       │   ├── views/
│       │   │   ├── studentpages/
│       │   │   │   ├── addStudent.jsp    # 添加学生页面
│       │   │   │   └── showStudent.jsp   # 显示学生页面
│       │   │   └── error/
│       │   │       ├── 404.jsp           # 404错误页面
│       │   │       └── 500.jsp           # 500错误页面
│       │   ├── student.xml               # Spring MVC配置
│       │   └── web.xml                   # Web应用配置
│       └── index.jsp                     # 首页
└── pom.xml                               # Maven配置文件
```

## 安装和运行

### 环境要求

- JDK 8 或更高版本
- Apache Maven 3.6+
- Apache Tomcat 8.5+
- IDE（推荐 IntelliJ IDEA 或 Eclipse）

### 运行步骤

1. **克隆或下载项目**
   ```bash
   # 如果是从Git仓库克隆
   git clone [repository-url]
   cd student-management
   ```

2. **编译项目**
   ```bash
   mvn clean compile
   ```

3. **打包项目**
   ```bash
   mvn clean package
   ```

4. **部署到Tomcat**
   - 将生成的 `target/student-management.war` 文件复制到Tomcat的 `webapps` 目录
   - 启动Tomcat服务器
   - 或者使用IDE直接运行

5. **访问应用**
   - 打开浏览器访问: `http://localhost:8080/student-management/`
   - 系统会自动跳转到添加学生信息页面

## 使用说明

### 添加学生信息

1. 访问系统首页，点击"开始使用系统"
2. 填写学生基本信息：
   - 学号：2022591854（已预填）
   - 姓名：何晨（已预填）
   - 年级：软工B2207班（已预填）
   - 班级：软工B2207班（已预填）
   - 性别：男（已预选）
   - 年龄：22（已预填）
3. 选择上传学生照片（可选）
4. 点击"保存学生信息"提交

### 查看和下载照片

1. 提交学生信息后，系统会跳转到学生信息详情页面
2. 页面左侧显示学生基本信息
3. 页面右侧显示学生照片（如果已上传）
4. 点击"下载照片"按钮可以下载原始照片文件

## 配置说明

### 文件上传配置

- 最大文件大小：10MB
- 支持的图片格式：JPG、PNG、GIF
- 上传目录：`webapp/uploads/photos/`

### Spring MVC配置

主要配置文件：`src/main/webapp/WEB-INF/student.xml`

- 视图解析器配置
- 文件上传解析器配置
- 静态资源处理配置
- 组件扫描配置

## 实验要求对应

本项目完全满足实验要求：

1. ✅ **学生类创建**: `com.springmvc.student.Student`
2. ✅ **控制器类创建**: `com.springmvc.student.StudentController`
3. ✅ **JSP页面创建**: `addStudent.jsp` 和 `showStudent.jsp`
4. ✅ **配置文件设置**: `student.xml` 和 `web.xml`
5. ✅ **功能测试**: 支持添加指定学生信息和照片上传下载

## 测试数据

系统已预填入实验要求的测试数据：
- 学号：2022591854
- 姓名：何晨
- 年级：软工B2207班
- 性别：男
- 年龄：22

## 故障排除

### 常见问题

1. **文件上传失败**
   - 检查文件大小是否超过10MB
   - 确认文件格式是否为支持的图片格式
   - 检查服务器磁盘空间

2. **页面无法访问**
   - 确认Tomcat服务器已启动
   - 检查端口是否被占用
   - 验证项目部署是否成功

3. **照片无法显示**
   - 检查上传目录权限
   - 确认静态资源配置正确
   - 查看浏览器控制台错误信息

## 开发者信息

- **课程**: Web应用开发实验
- **实验内容**: Spring MVC文件上传下载
- **开发时间**: 2024年

## 许可证

本项目仅用于教学实验目的。

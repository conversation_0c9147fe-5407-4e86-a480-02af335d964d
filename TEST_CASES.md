# 学生管理系统测试用例

## 测试环境
- 浏览器：Chrome/Firefox/Edge
- 服务器：Tomcat 8.5+
- JDK：1.8+

## 功能测试用例

### 测试用例1：系统首页访问
**测试目标**: 验证系统首页能正常访问和显示

**测试步骤**:
1. 启动Tomcat服务器
2. 在浏览器中访问 `http://localhost:8080/student-management/`

**预期结果**:
- 页面正常加载，显示"学生管理系统"标题
- 显示系统功能介绍
- "开始使用系统"按钮可见且可点击
- 页面样式正常，中文显示正确

### 测试用例2：添加学生信息页面
**测试目标**: 验证添加学生信息页面的显示和表单功能

**测试步骤**:
1. 从首页点击"开始使用系统"按钮
2. 或直接访问 `http://localhost:8080/student-management/student/add`

**预期结果**:
- 页面正常跳转到添加学生信息页面
- 表单字段已预填入测试数据：
  - 学号：2022591854
  - 姓名：何晨
  - 年级：软工B2207班
  - 班级：软工B2207班
  - 性别：男（已选中）
  - 年龄：22
- 文件上传控件正常显示
- "保存学生信息"按钮可见

### 测试用例3：学生信息提交（无照片）
**测试目标**: 验证不上传照片时的学生信息保存功能

**测试步骤**:
1. 在添加学生信息页面，保持预填数据不变
2. 不选择照片文件
3. 点击"保存学生信息"按钮

**预期结果**:
- 页面跳转到学生信息详情页面
- 显示成功保存消息
- 左侧正确显示所有学生信息
- 右侧显示"暂无照片"提示
- 不显示下载照片按钮

### 测试用例4：学生信息提交（有照片）
**测试目标**: 验证上传照片时的学生信息保存功能

**测试步骤**:
1. 在添加学生信息页面，保持预填数据不变
2. 选择一张图片文件（JPG/PNG/GIF格式，小于10MB）
3. 点击"保存学生信息"按钮

**预期结果**:
- 页面跳转到学生信息详情页面
- 显示成功保存消息
- 左侧正确显示所有学生信息
- 右侧正确显示上传的照片
- 显示照片文件名
- "下载照片"按钮可见且可点击

### 测试用例5：照片下载功能
**测试目标**: 验证照片下载功能

**前提条件**: 已成功上传学生照片

**测试步骤**:
1. 在学生信息详情页面
2. 点击"下载照片"按钮

**预期结果**:
- 浏览器开始下载照片文件
- 下载的文件名格式为：学号.扩展名（如：2022591854.jpg）
- 下载的文件可以正常打开和查看
- 文件内容与上传的照片一致

### 测试用例6：照片显示功能
**测试目标**: 验证照片在页面中的显示功能

**前提条件**: 已成功上传学生照片

**测试步骤**:
1. 在学生信息详情页面查看照片显示区域

**预期结果**:
- 照片正确显示在页面右侧
- 照片尺寸适当，不超出显示区域
- 照片清晰，无失真
- 照片加载速度正常

## 异常测试用例

### 测试用例7：文件大小限制测试
**测试目标**: 验证文件大小限制功能

**测试步骤**:
1. 选择一个大于10MB的图片文件
2. 尝试上传并提交

**预期结果**:
- 系统应该拒绝上传
- 显示相应的错误提示信息
- 页面不会跳转，停留在添加页面

### 测试用例8：文件格式限制测试
**测试目标**: 验证文件格式限制功能

**测试步骤**:
1. 选择一个非图片格式的文件（如.txt, .doc等）
2. 尝试上传并提交

**预期结果**:
- 浏览器文件选择对话框应该只显示图片格式文件
- 或系统拒绝非图片格式文件的上传

### 测试用例9：必填字段验证
**测试目标**: 验证必填字段的验证功能

**测试步骤**:
1. 清空一个或多个必填字段（学号、姓名等）
2. 尝试提交表单

**预期结果**:
- 浏览器显示字段验证错误提示
- 表单不会提交
- 用户被提示填写必填字段

### 测试用例10：特殊字符处理
**测试目标**: 验证特殊字符的处理

**测试步骤**:
1. 在姓名字段输入特殊字符（如：<script>、&、"等）
2. 提交表单

**预期结果**:
- 特殊字符被正确处理，不会导致页面错误
- 在显示页面中特殊字符被正确转义显示
- 不会出现XSS安全问题

## 兼容性测试

### 测试用例11：浏览器兼容性
**测试目标**: 验证不同浏览器的兼容性

**测试步骤**:
1. 分别在Chrome、Firefox、Edge浏览器中进行基本功能测试

**预期结果**:
- 所有浏览器中页面显示正常
- 功能在所有浏览器中都能正常工作
- 样式在不同浏览器中保持一致

### 测试用例12：响应式设计测试
**测试目标**: 验证页面的响应式设计

**测试步骤**:
1. 调整浏览器窗口大小
2. 使用浏览器开发者工具模拟移动设备

**预期结果**:
- 页面在不同屏幕尺寸下都能正常显示
- 移动设备上操作便捷
- 文字和按钮大小适中

## 性能测试

### 测试用例13：页面加载性能
**测试目标**: 验证页面加载性能

**测试步骤**:
1. 使用浏览器开发者工具监控页面加载时间
2. 测试不同大小图片的上传时间

**预期结果**:
- 页面加载时间在可接受范围内（< 3秒）
- 图片上传响应及时
- 没有明显的性能瓶颈

## 测试报告模板

### 测试执行记录
| 测试用例编号 | 测试用例名称 | 执行结果 | 实际结果 | 备注 |
|-------------|-------------|----------|----------|------|
| TC001 | 系统首页访问 | ✅ PASS | 符合预期 | |
| TC002 | 添加学生信息页面 | ✅ PASS | 符合预期 | |
| TC003 | 学生信息提交（无照片） | ✅ PASS | 符合预期 | |
| TC004 | 学生信息提交（有照片） | ✅ PASS | 符合预期 | |
| TC005 | 照片下载功能 | ✅ PASS | 符合预期 | |
| ... | ... | ... | ... | ... |

### 缺陷记录
| 缺陷编号 | 缺陷描述 | 严重程度 | 状态 | 修复说明 |
|---------|----------|----------|------|----------|
| BUG001 | 描述发现的问题 | 高/中/低 | 开放/已修复 | 修复方案 |

### 测试总结
- 测试用例总数：13
- 通过用例数：X
- 失败用例数：Y
- 发现缺陷数：Z
- 整体质量评估：良好/一般/需改进

package com.springmvc.student;

/**
 * 学生实体类
 * 包含学号、姓名、年级、班级、性别、年龄、照片相关属性
 */
public class Student {
    private String studentId;      // 学号
    private String name;           // 姓名
    private String grade;          // 年级
    private String className;      // 班级
    private String gender;         // 性别
    private int age;              // 年龄
    private byte[] photo;         // 照片数据
    private String photoName;     // 照片名称
    private String photoPath;     // 照片映射路径
    
    // 默认构造函数
    public Student() {
    }
    
    // 带参数的构造函数
    public Student(String studentId, String name, String grade, String className, 
                   String gender, int age) {
        this.studentId = studentId;
        this.name = name;
        this.grade = grade;
        this.className = className;
        this.gender = gender;
        this.age = age;
    }
    
    // Getter和Setter方法
    public String getStudentId() {
        return studentId;
    }
    
    public void setStudentId(String studentId) {
        this.studentId = studentId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getGrade() {
        return grade;
    }
    
    public void setGrade(String grade) {
        this.grade = grade;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
    
    public String getGender() {
        return gender;
    }
    
    public void setGender(String gender) {
        this.gender = gender;
    }
    
    public int getAge() {
        return age;
    }
    
    public void setAge(int age) {
        this.age = age;
    }
    
    public byte[] getPhoto() {
        return photo;
    }
    
    public void setPhoto(byte[] photo) {
        this.photo = photo;
    }
    
    public String getPhotoName() {
        return photoName;
    }
    
    public void setPhotoName(String photoName) {
        this.photoName = photoName;
    }
    
    public String getPhotoPath() {
        return photoPath;
    }
    
    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }
    
    @Override
    public String toString() {
        return "Student{" +
                "studentId='" + studentId + '\'' +
                ", name='" + name + '\'' +
                ", grade='" + grade + '\'' +
                ", className='" + className + '\'' +
                ", gender='" + gender + '\'' +
                ", age=" + age +
                ", photoName='" + photoName + '\'' +
                ", photoPath='" + photoPath + '\'' +
                '}';
    }
}

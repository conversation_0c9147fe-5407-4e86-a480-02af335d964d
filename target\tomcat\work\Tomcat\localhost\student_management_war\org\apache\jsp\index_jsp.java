/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-05-31 08:26:29 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class index_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"zh-CN\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>学生管理系统</title>\n");
      out.write("    <style>\n");
      out.write("        body {\n");
      out.write("            font-family: 'Microsoft YaHei', Arial, sans-serif;\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            margin: 0;\n");
      out.write("            padding: 0;\n");
      out.write("            height: 100vh;\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            justify-content: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .welcome-container {\n");
      out.write("            background-color: white;\n");
      out.write("            padding: 50px;\n");
      out.write("            border-radius: 15px;\n");
      out.write("            box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n");
      out.write("            text-align: center;\n");
      out.write("            max-width: 500px;\n");
      out.write("            width: 90%;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        h1 {\n");
      out.write("            color: #333;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("            font-size: 2.5em;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .subtitle {\n");
      out.write("            color: #666;\n");
      out.write("            margin-bottom: 30px;\n");
      out.write("            font-size: 1.2em;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .feature-list {\n");
      out.write("            text-align: left;\n");
      out.write("            margin: 30px 0;\n");
      out.write("            padding: 20px;\n");
      out.write("            background-color: #f8f9fa;\n");
      out.write("            border-radius: 8px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .feature-list h3 {\n");
      out.write("            color: #4CAF50;\n");
      out.write("            margin-bottom: 15px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .feature-list ul {\n");
      out.write("            list-style-type: none;\n");
      out.write("            padding: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .feature-list li {\n");
      out.write("            padding: 8px 0;\n");
      out.write("            border-bottom: 1px solid #eee;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .feature-list li:last-child {\n");
      out.write("            border-bottom: none;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .feature-list li:before {\n");
      out.write("            content: \"✓ \";\n");
      out.write("            color: #4CAF50;\n");
      out.write("            font-weight: bold;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .start-btn {\n");
      out.write("            background: linear-gradient(45deg, #4CAF50, #45a049);\n");
      out.write("            color: white;\n");
      out.write("            padding: 15px 40px;\n");
      out.write("            border: none;\n");
      out.write("            border-radius: 25px;\n");
      out.write("            font-size: 18px;\n");
      out.write("            cursor: pointer;\n");
      out.write("            text-decoration: none;\n");
      out.write("            display: inline-block;\n");
      out.write("            transition: transform 0.3s, box-shadow 0.3s;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .start-btn:hover {\n");
      out.write("            transform: translateY(-2px);\n");
      out.write("            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);\n");
      out.write("            text-decoration: none;\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .footer {\n");
      out.write("            margin-top: 30px;\n");
      out.write("            color: #999;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <div class=\"welcome-container\">\n");
      out.write("        <h1>🎓 学生管理系统</h1>\n");
      out.write("        <p class=\"subtitle\">基于Spring MVC的学生信息管理平台</p>\n");
      out.write("        \n");
      out.write("        <div class=\"feature-list\">\n");
      out.write("            <h3>系统功能</h3>\n");
      out.write("            <ul>\n");
      out.write("                <li>学生信息录入与管理</li>\n");
      out.write("                <li>学生照片上传功能</li>\n");
      out.write("                <li>照片在线预览</li>\n");
      out.write("                <li>照片文件下载</li>\n");
      out.write("                <li>响应式界面设计</li>\n");
      out.write("            </ul>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <a href=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/student/add\" class=\"start-btn\">\n");
      out.write("            开始使用系统\n");
      out.write("        </a>\n");
      out.write("        \n");
      out.write("        <div class=\"footer\">\n");
      out.write("            <p>Spring MVC + JSP + 文件上传下载</p>\n");
      out.write("            <p>实验课程：Web应用开发</p>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}

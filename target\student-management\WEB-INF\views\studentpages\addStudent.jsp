<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加学生信息</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        input[type="text"], input[type="number"], select, input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="number"]:focus, select:focus {
            border-color: #4CAF50;
            outline: none;
            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
        }
        
        .radio-group label {
            display: flex;
            align-items: center;
            font-weight: normal;
            margin-bottom: 0;
        }
        
        .radio-group input[type="radio"] {
            width: auto;
            margin-right: 5px;
        }
        
        .file-upload {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            background-color: #fafafa;
        }
        
        .file-upload:hover {
            border-color: #4CAF50;
            background-color: #f0f8f0;
        }
        
        .submit-btn {
            background-color: #4CAF50;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 20px;
        }
        
        .submit-btn:hover {
            background-color: #45a049;
        }
        
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .required {
            color: red;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>学生信息管理系统 - 添加学生</h1>
        
        <!-- 错误信息显示 -->
        <c:if test="${not empty error}">
            <div class="error">${error}</div>
        </c:if>
        
        <form action="${pageContext.request.contextPath}/student/save" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="studentId">学号 <span class="required">*</span></label>
                <input type="text" id="studentId" name="studentId" value="2022591854" required>
            </div>
            
            <div class="form-group">
                <label for="name">姓名 <span class="required">*</span></label>
                <input type="text" id="name" name="name" value="何晨" required>
            </div>
            
            <div class="form-group">
                <label for="grade">年级 <span class="required">*</span></label>
                <input type="text" id="grade" name="grade" value="软工B2207班" required>
            </div>
            
            <div class="form-group">
                <label for="className">班级 <span class="required">*</span></label>
                <input type="text" id="className" name="className" value="软工B2207班" required>
            </div>
            
            <div class="form-group">
                <label>性别 <span class="required">*</span></label>
                <div class="radio-group">
                    <label>
                        <input type="radio" name="gender" value="男" checked required> 男
                    </label>
                    <label>
                        <input type="radio" name="gender" value="女" required> 女
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="age">年龄 <span class="required">*</span></label>
                <input type="number" id="age" name="age" value="22" min="1" max="100" required>
            </div>
            
            <div class="form-group">
                <label for="photo">上传照片</label>
                <div class="file-upload">
                    <input type="file" id="photo" name="photo" accept="image/*">
                    <p>点击选择照片文件或拖拽文件到此处</p>
                    <small>支持 JPG、PNG、GIF 格式，文件大小不超过 10MB</small>
                </div>
            </div>
            
            <button type="submit" class="submit-btn">保存学生信息</button>
        </form>
    </div>
    
    <script>
        // 文件选择预览功能
        document.getElementById('photo').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const fileUpload = document.querySelector('.file-upload');
                fileUpload.innerHTML = `
                    <input type="file" id="photo" name="photo" accept="image/*" style="display: none;">
                    <p><strong>已选择文件：</strong>${file.name}</p>
                    <p>文件大小：${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <button type="button" onclick="resetFileInput()">重新选择</button>
                `;
            }
        });
        
        function resetFileInput() {
            const fileUpload = document.querySelector('.file-upload');
            fileUpload.innerHTML = `
                <input type="file" id="photo" name="photo" accept="image/*">
                <p>点击选择照片文件或拖拽文件到此处</p>
                <small>支持 JPG、PNG、GIF 格式，文件大小不超过 10MB</small>
            `;
            document.getElementById('photo').addEventListener('change', arguments.callee.caller);
        }
    </script>
</body>
</html>

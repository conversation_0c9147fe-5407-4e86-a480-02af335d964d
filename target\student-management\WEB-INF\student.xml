<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd
           http://www.springframework.org/schema/mvc
           http://www.springframework.org/schema/mvc/spring-mvc.xsd">

    <!-- 启用注解驱动 -->
    <mvc:annotation-driven />
    
    <!-- 组件扫描 -->
    <context:component-scan base-package="com.springmvc.student" />
    
    <!-- 视图解析器 -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
        <property name="prefix" value="/WEB-INF/views/" />
        <property name="suffix" value=".jsp" />
    </bean>
    
    <!-- 文件上传解析器 -->
    <bean id="multipartResolver" 
          class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <!-- 设置上传文件的最大尺寸为10MB -->
        <property name="maxUploadSize" value="10485760" />
        <!-- 设置上传文件的编码 -->
        <property name="defaultEncoding" value="UTF-8" />
        <!-- 设置内存中最大缓存大小 -->
        <property name="maxInMemorySize" value="1048576" />
    </bean>
    
    <!-- 静态资源处理 - 不拦截图片等静态资源 -->
    <mvc:resources mapping="/uploads/**" location="/uploads/" />
    <mvc:resources mapping="/css/**" location="/css/" />
    <mvc:resources mapping="/js/**" location="/js/" />
    <mvc:resources mapping="/images/**" location="/images/" />
    
    <!-- 视图控制器配置 -->
    <mvc:view-controller path="/" view-name="redirect:/student/add" />
    <mvc:view-controller path="/index" view-name="redirect:/student/add" />
    
    <!-- 默认servlet处理器 -->
    <mvc:default-servlet-handler />
    
</beans>

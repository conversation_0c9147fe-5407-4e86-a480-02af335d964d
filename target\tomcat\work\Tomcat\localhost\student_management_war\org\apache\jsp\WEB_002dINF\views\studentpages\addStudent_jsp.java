/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-05-31 08:03:34 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.WEB_002dINF.views.studentpages;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class addStudent_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html;charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"zh-CN\">\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
      out.write("    <title>添加学生信息</title>\n");
      out.write("    <style>\n");
      out.write("        body {\n");
      out.write("            font-family: 'Microsoft YaHei', Arial, sans-serif;\n");
      out.write("            background-color: #f5f5f5;\n");
      out.write("            margin: 0;\n");
      out.write("            padding: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .container {\n");
      out.write("            max-width: 600px;\n");
      out.write("            margin: 0 auto;\n");
      out.write("            background-color: white;\n");
      out.write("            padding: 30px;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            box-shadow: 0 0 20px rgba(0,0,0,0.1);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        h1 {\n");
      out.write("            text-align: center;\n");
      out.write("            color: #333;\n");
      out.write("            margin-bottom: 30px;\n");
      out.write("            border-bottom: 2px solid #4CAF50;\n");
      out.write("            padding-bottom: 10px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-group {\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        label {\n");
      out.write("            display: block;\n");
      out.write("            margin-bottom: 5px;\n");
      out.write("            font-weight: bold;\n");
      out.write("            color: #555;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        input[type=\"text\"], input[type=\"number\"], select, input[type=\"file\"] {\n");
      out.write("            width: 100%;\n");
      out.write("            padding: 10px;\n");
      out.write("            border: 1px solid #ddd;\n");
      out.write("            border-radius: 5px;\n");
      out.write("            font-size: 16px;\n");
      out.write("            box-sizing: border-box;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        input[type=\"text\"]:focus, input[type=\"number\"]:focus, select:focus {\n");
      out.write("            border-color: #4CAF50;\n");
      out.write("            outline: none;\n");
      out.write("            box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .radio-group {\n");
      out.write("            display: flex;\n");
      out.write("            gap: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .radio-group label {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            font-weight: normal;\n");
      out.write("            margin-bottom: 0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .radio-group input[type=\"radio\"] {\n");
      out.write("            width: auto;\n");
      out.write("            margin-right: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .file-upload {\n");
      out.write("            border: 2px dashed #ddd;\n");
      out.write("            padding: 20px;\n");
      out.write("            text-align: center;\n");
      out.write("            border-radius: 5px;\n");
      out.write("            background-color: #fafafa;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .file-upload:hover {\n");
      out.write("            border-color: #4CAF50;\n");
      out.write("            background-color: #f0f8f0;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .submit-btn {\n");
      out.write("            background-color: #4CAF50;\n");
      out.write("            color: white;\n");
      out.write("            padding: 12px 30px;\n");
      out.write("            border: none;\n");
      out.write("            border-radius: 5px;\n");
      out.write("            cursor: pointer;\n");
      out.write("            font-size: 16px;\n");
      out.write("            width: 100%;\n");
      out.write("            margin-top: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .submit-btn:hover {\n");
      out.write("            background-color: #45a049;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .error {\n");
      out.write("            color: #d32f2f;\n");
      out.write("            background-color: #ffebee;\n");
      out.write("            padding: 10px;\n");
      out.write("            border-radius: 5px;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .required {\n");
      out.write("            color: red;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <h1>学生信息管理系统 - 添加学生</h1>\n");
      out.write("        \n");
      out.write("        <!-- 错误信息显示 -->\n");
      out.write("        ");
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write("\n");
      out.write("        \n");
      out.write("        <form action=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${pageContext.request.contextPath}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("/student/save\" method=\"post\" enctype=\"multipart/form-data\">\n");
      out.write("            <div class=\"form-group\">\n");
      out.write("                <label for=\"studentId\">学号 <span class=\"required\">*</span></label>\n");
      out.write("                <input type=\"text\" id=\"studentId\" name=\"studentId\" value=\"2022591854\" required>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <div class=\"form-group\">\n");
      out.write("                <label for=\"name\">姓名 <span class=\"required\">*</span></label>\n");
      out.write("                <input type=\"text\" id=\"name\" name=\"name\" value=\"何晨\" required>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <div class=\"form-group\">\n");
      out.write("                <label for=\"grade\">年级 <span class=\"required\">*</span></label>\n");
      out.write("                <input type=\"text\" id=\"grade\" name=\"grade\" value=\"软工B2207班\" required>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <div class=\"form-group\">\n");
      out.write("                <label for=\"className\">班级 <span class=\"required\">*</span></label>\n");
      out.write("                <input type=\"text\" id=\"className\" name=\"className\" value=\"软工B2207班\" required>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <div class=\"form-group\">\n");
      out.write("                <label>性别 <span class=\"required\">*</span></label>\n");
      out.write("                <div class=\"radio-group\">\n");
      out.write("                    <label>\n");
      out.write("                        <input type=\"radio\" name=\"gender\" value=\"男\" checked required> 男\n");
      out.write("                    </label>\n");
      out.write("                    <label>\n");
      out.write("                        <input type=\"radio\" name=\"gender\" value=\"女\" required> 女\n");
      out.write("                    </label>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <div class=\"form-group\">\n");
      out.write("                <label for=\"age\">年龄 <span class=\"required\">*</span></label>\n");
      out.write("                <input type=\"number\" id=\"age\" name=\"age\" value=\"22\" min=\"1\" max=\"100\" required>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <div class=\"form-group\">\n");
      out.write("                <label for=\"photo\">上传照片</label>\n");
      out.write("                <div class=\"file-upload\">\n");
      out.write("                    <input type=\"file\" id=\"photo\" name=\"photo\" accept=\"image/*\">\n");
      out.write("                    <p>点击选择照片文件或拖拽文件到此处</p>\n");
      out.write("                    <small>支持 JPG、PNG、GIF 格式，文件大小不超过 10MB</small>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <button type=\"submit\" class=\"submit-btn\">保存学生信息</button>\n");
      out.write("        </form>\n");
      out.write("    </div>\n");
      out.write("    \n");
      out.write("    <script>\n");
      out.write("        // 文件选择预览功能\n");
      out.write("        document.getElementById('photo').addEventListener('change', function(e) {\n");
      out.write("            const file = e.target.files[0];\n");
      out.write("            if (file) {\n");
      out.write("                const fileUpload = document.querySelector('.file-upload');\n");
      out.write("                fileUpload.innerHTML =\n");
      out.write("                    '<input type=\"file\" id=\"photo\" name=\"photo\" accept=\"image/*\" style=\"display: none;\">' +\n");
      out.write("                    '<p><strong>已选择文件：</strong>' + file.name + '</p>' +\n");
      out.write("                    '<p>文件大小：' + (file.size / 1024 / 1024).toFixed(2) + ' MB</p>' +\n");
      out.write("                    '<button type=\"button\" onclick=\"resetFileInput()\">重新选择</button>';\n");
      out.write("            }\n");
      out.write("        });\n");
      out.write("        \n");
      out.write("        function resetFileInput() {\n");
      out.write("            const fileUpload = document.querySelector('.file-upload');\n");
      out.write("            fileUpload.innerHTML =\n");
      out.write("                '<input type=\"file\" id=\"photo\" name=\"photo\" accept=\"image/*\">' +\n");
      out.write("                '<p>点击选择照片文件或拖拽文件到此处</p>' +\n");
      out.write("                '<small>支持 JPG、PNG、GIF 格式，文件大小不超过 10MB</small>';\n");
      out.write("            document.getElementById('photo').addEventListener('change', arguments.callee.caller);\n");
      out.write("        }\n");
      out.write("    </script>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_c_005fif_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f0.setParent(null);
    // /WEB-INF/views/studentpages/addStudent.jsp(124,8) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty error}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
    if (_jspx_eval_c_005fif_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("            <div class=\"error\">");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${error}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("</div>\n");
        out.write("        ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
    return false;
  }
}

# 学生管理系统部署指南

## 快速部署步骤

### 方法一：使用IDE（推荐）

#### 使用IntelliJ IDEA

1. **导入项目**
   - 打开IntelliJ IDEA
   - 选择 "Open" 或 "Import Project"
   - 选择项目根目录（包含pom.xml的目录）
   - 选择 "Import project from external model" -> "Maven"
   - 点击 "Next" 直到完成导入

2. **配置Tomcat**
   - 点击 "Run" -> "Edit Configurations"
   - 点击 "+" -> "Tomcat Server" -> "Local"
   - 配置Tomcat安装路径
   - 在 "Deployment" 标签页中，点击 "+" -> "Artifact"
   - 选择 "student-management:war exploded"
   - 设置 Application context 为 "/student-management"

3. **运行项目**
   - 点击绿色运行按钮
   - 浏览器会自动打开 `http://localhost:8080/student-management/`

#### 使用Eclipse

1. **导入项目**
   - 打开Eclipse
   - File -> Import -> Existing Maven Projects
   - 选择项目根目录
   - 点击 "Finish"

2. **配置Tomcat**
   - 右键项目 -> Properties -> Project Facets
   - 启用 "Java" 和 "Dynamic Web Module"
   - 右键项目 -> Run As -> Run on Server
   - 选择Tomcat服务器

### 方法二：命令行部署

#### 前提条件
- 已安装JDK 8+
- 已安装Maven 3.6+
- 已安装Tomcat 8.5+

#### 步骤

1. **编译项目**
   ```bash
   cd d:\demo9
   mvn clean compile
   ```

2. **打包项目**
   ```bash
   mvn clean package
   ```

3. **部署到Tomcat**
   ```bash
   # 复制WAR文件到Tomcat webapps目录
   copy target\student-management.war %TOMCAT_HOME%\webapps\
   
   # 启动Tomcat
   %TOMCAT_HOME%\bin\startup.bat
   ```

4. **访问应用**
   - 打开浏览器访问: `http://localhost:8080/student-management/`

### 方法三：手动部署（如果Maven不可用）

1. **创建目录结构**
   ```
   student-management/
   ├── WEB-INF/
   │   ├── classes/
   │   │   └── com/springmvc/student/
   │   ├── lib/
   │   ├── views/
   │   ├── web.xml
   │   └── student.xml
   └── index.jsp
   ```

2. **编译Java文件**
   - 使用IDE编译Java源文件
   - 将编译后的.class文件放入WEB-INF/classes/对应包目录

3. **下载依赖JAR包**
   需要以下JAR包放入WEB-INF/lib/目录：
   - spring-webmvc-5.3.21.jar
   - spring-context-5.3.21.jar
   - spring-core-5.3.21.jar
   - spring-beans-5.3.21.jar
   - spring-web-5.3.21.jar
   - spring-expression-5.3.21.jar
   - commons-fileupload-1.4.jar
   - commons-io-2.11.0.jar
   - jstl-1.2.jar

4. **部署到Tomcat**
   - 将整个student-management目录复制到Tomcat的webapps目录
   - 启动Tomcat

## 测试步骤

1. **访问首页**
   - URL: `http://localhost:8080/student-management/`
   - 应该看到欢迎页面

2. **添加学生信息**
   - 点击"开始使用系统"
   - 填写学生信息（已预填测试数据）
   - 选择一张照片上传
   - 点击"保存学生信息"

3. **查看学生信息**
   - 系统会跳转到学生信息详情页面
   - 左侧显示学生基本信息
   - 右侧显示上传的照片

4. **下载照片**
   - 点击"下载照片"按钮
   - 浏览器会下载照片文件

## 常见问题解决

### 1. 端口冲突
如果8080端口被占用，修改Tomcat的server.xml文件中的端口号：
```xml
<Connector port="8081" protocol="HTTP/1.1" />
```

### 2. 文件上传失败
- 检查uploads目录权限
- 确认文件大小不超过10MB
- 检查文件格式是否为图片格式

### 3. 页面404错误
- 确认项目部署成功
- 检查URL路径是否正确
- 查看Tomcat日志文件

### 4. 中文乱码
- 确认所有文件都使用UTF-8编码
- 检查web.xml中的字符编码过滤器配置

## 项目验证清单

- [ ] 项目成功启动，无错误日志
- [ ] 首页可以正常访问
- [ ] 添加学生页面显示正常，表单预填数据正确
- [ ] 可以成功上传照片
- [ ] 学生信息保存成功，跳转到详情页面
- [ ] 照片可以正常显示
- [ ] 照片下载功能正常工作
- [ ] 所有页面样式显示正常
- [ ] 中文字符显示正常

## 技术支持

如果遇到问题，请检查：
1. Tomcat日志文件（logs/catalina.out）
2. 浏览器开发者工具控制台
3. 项目文件结构是否完整
4. 依赖JAR包是否齐全

## 实验报告要点

完成部署后，实验报告应包含：
1. 项目结构截图
2. 添加学生信息页面截图
3. 学生信息详情页面截图
4. 照片上传和下载功能演示
5. 遇到的问题及解决方案
